{"j-dingtalk-web_pages_aiImage_PageTitle": "AI画像生成", "j-dingtalk-web_pages_aiImage_Loading": "加载中...", "j-dingtalk-web_pages_aiImage_FailedToObtainTheImage": "获取图片失败", "j-dingtalk-web_pages_aiImage_PleaseEnterADescription": "请输入创意描述", "j-dingtalk-web_pages_aiImage_FailedToGenerate": "生成失败", "j-dingtalk-web_pages_aiImage_FailedToGeneratePleaseTry": "生成失败，请重试", "j-dingtalk-web_pages_aiImage_ImageGenerationFailedPleaseTry": "图片生成失败，请重试", "j-dingtalk-web_pages_aiImage_components_ImageForm_UploadOriginalImage": "上传原始图片", "j-dingtalk-web_pages_aiImage_components_ImageForm_ClickToUploadImage": "点击上传图片", "j-dingtalk-web_pages_aiImage_components_ImageForm_YourCreativeDescription": "你的创意描述", "j-dingtalk-web_pages_aiImage_components_ImageForm_SelectTemplateOrDescribe": "选择下方模版，或自由输入", "j-dingtalk-web_pages_aiImage_components_ImageForm_BackgroundTemplate": "背景图片模板", "j-dingtalk-web_pages_aiImage_components_ImageForm_ImageSize": "图片尺寸", "j-dingtalk-web_pages_aiImage_components_ImageForm_Generating": "生成中...", "j-dingtalk-web_pages_aiImage_components_ImageForm_GenerateNow": "立即生成", "j-dingtalk-web_pages_aiImage_components_ImageForm_PleaseEnterADescription": "请输入创意描述", "j-dingtalk-web_pages_aiImage_components_TemplateSelector_None": "无", "j-dingtalk-web_pages_aiImage_components_TemplateSelector_NoTemplatesAvailable": "暂无可用模板", "j-dingtalk-web_pages_aiImage_components_WelcomeScreen_AIImageGeneration": "AI图片生成", "j-dingtalk-web_pages_aiImage_components_WelcomeScreen_CreateBeautifulImages": "使用AI技术创建精美图片", "j-dingtalk-web_pages_aiImage_components_WelcomeScreen_TextToImage": "文字转图片", "j-dingtalk-web_pages_aiImage_components_WelcomeScreen_MultipleFormats": "多种格式支持", "j-dingtalk-web_pages_aiImage_components_WelcomeScreen_HighQuality": "高质量输出", "j-dingtalk-web_pages_aiImage_components_WelcomeScreen_StartCreating": "开始创作", "j-dingtalk-web_pages_aiImage_components_ImageList_MyImages": "我的图片", "j-dingtalk-web_pages_aiImage_components_ImageList_Items": "个", "j-dingtalk-web_pages_aiImage_components_ImageList_CreateNew": "新建", "j-dingtalk-web_pages_aiImage_components_ImageList_NoImagesYet": "还没有图片", "j-dingtalk-web_pages_aiImage_components_ImageList_StartCreatingImages": "开始创建您的第一张AI图片", "j-dingtalk-web_pages_aiImage_components_ImageList_CreateFirstImage": "创建第一张图片", "j-dingtalk-web_pages_aiImage_components_ImageList_FailedToLoadImages": "加载图片失败", "j-dingtalk-web_pages_aiImage_components_ImageList_Retry": "重试", "j-dingtalk-web_pages_aiImage_components_ImageList_Loading": "加载中...", "j-dingtalk-web_pages_aiImage_components_ImageList_LoadMore": "加载更多", "j-dingtalk-web_pages_aiImage_components_ImageItem_ConfirmDelete": "确认删除", "j-dingtalk-web_pages_aiImage_components_ImageItem_DeleteWarning": "删除后无法恢复，确定要删除这张图片吗？", "j-dingtalk-web_pages_aiImage_components_ImageItem_Like": "喜欢", "j-dingtalk-web_pages_aiImage_components_ImageItem_Dislike": "不喜欢", "j-dingtalk-web_pages_aiImage_components_ImageItem_Download": "下载", "j-dingtalk-web_pages_aiImage_components_ImageItem_Regenerate": "重新生成", "j-dingtalk-web_pages_aiImage_components_ImageItem_Delete": "删除", "j-dingtalk-web_pages_aiImage_components_ImageItem_Generating": "生成中...", "j-dingtalk-web_pages_aiImage_components_ImageItem_GenerationFailed": "生成失败", "j-dingtalk-web_pages_aiImage_components_ImageItem_DownloadStarted": "开始下载", "j-dingtalk-web_pages_aiImage_components_ImageItem_Size": "尺寸", "j-dingtalk-web_pages_aiImage_components_ImageItem_Style": "风格", "j-dingtalk-web_pages_aiImage_components_ImageItem_Created": "创建时间", "j-dingtalk-web_components_aiMaterial_ImageUploader_PleaseUseInDingTalk": "请在钉钉中使用", "j-dingtalk-web_components_aiMaterial_ImageUploader_ApiNotConfigured": "API未配置", "j-dingtalk-web_components_aiMaterial_ImageUploader_ImageUploadedSuccessfully": "图片上传成功", "j-dingtalk-web_components_aiMaterial_ImageUploader_ImageUploadFailed": "图片上传失败", "j-dingtalk-web_components_aiMaterial_ImageUploader_ClickUploadImage": "点击上传图片", "j-dingtalk-web_components_aiMaterial_ImageUploader_Uploading": "上传中...", "j-dingtalk-web_components_aiMaterial_PromptInput_EnterYourCreativeDescription": "输入您的创意描述", "j-dingtalk-web_components_aiMaterial_PromptInput_TipDescribeYourIdea": "详细描述您想要的图片内容，包括风格、颜色、构图等"}