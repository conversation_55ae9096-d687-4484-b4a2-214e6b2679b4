// AI素材相关类型定义 - AI Material Type Definitions

// 基础素材接口 - Base material interface
interface BaseMaterial {
  uuid: string;
  createdAt: string;
  status: 'pending' | 'processing' | 'finish' | 'failed';
  positivePrompt: string;
  negativePrompt?: string;
  userRating?: 'like' | 'dislike';
  requestId?: string;
}

// 视频素材接口 - Video material interface  
export interface VideoMaterial extends BaseMaterial {
  type: 'video';
  videoUrl: string;
  imageUrl: string;      // 首帧图片
  gifUrl?: string;
  quality: 'standard' | 'high';
  duration: number;
  videoFinishTime?: string;
}

// 图片素材接口 - Image material interface
export interface ImageMaterial extends BaseMaterial {
  type: 'image';
  imageUrl: string;
  originalImageUrl?: string;  // 原始参考图片
  style: 'realistic' | 'anime' | 'oil_painting' | 'watercolor' | 'sketch';
  size: '800x800' | '750x1000' | '1280x720';
  quality: 'standard' | 'high';
  templateId?: string;        // 背景模板ID
}

// 联合类型 - Union type
export type AIMaterial = VideoMaterial | ImageMaterial;

// 图片生成参数 - Image generation parameters
export interface ImageGenerationParams {
  prompt: string;           // 正向提示词
  negativePrompt?: string;  // 负向提示词  
  style: string;           // 图片风格
  size: string;            // 图片尺寸
  quality: string;         // 生成质量
  originalImageUrl?: string; // 原始参考图片
  templateId?: string;     // 背景模板ID
}

// 背景模板接口 - Background template interface
export interface BackgroundTemplate {
  id: string;
  name: string;
  thumbnailUrl: string;
  category: 'nature' | 'urban' | 'abstract' | 'minimal' | 'texture';
  description?: string;
}

// 素材统计接口 - Material statistics interface
export interface MaterialStats {
  totalCount: number;
  monthlyCount: number;
  videoCount: number;
  imageCount: number;
  successRate: number;
}

// 分页参数 - Pagination parameters
export interface PaginationParams {
  nextUuid?: string;
  pageSize?: number;
}

// API响应基础接口 - Base API response interface
export interface BaseApiResponse {
  success: boolean;
  errorMsg?: string;
  data?: any;
}

// 生成结果响应 - Generation result response
export interface GenerationResponse extends BaseApiResponse {
  uuid?: string;
  taskId?: string;
}

// 列表响应 - List response
export interface ListResponse<T> extends BaseApiResponse {
  list?: T[];
  nextUuid?: string;
  hasNextPage?: boolean;
}
