import request from './base';
import { AIMaterial, MaterialStats, ListResponse, BaseApiResponse } from '@/types/aiMaterial';

// 获取所有素材列表 - Get all materials list (videos + images)
export const listAllMaterials = (data: {
  nextUuid?: string;
  pageSize?: number;
  type?: 'video' | 'image';
} = {}): Promise<ListResponse<AIMaterial>> => {
  // Mock implementation for development
  if (process.env.NODE_ENV === 'development') {
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockMaterials: AIMaterial[] = [
          {
            uuid: `video_mock_${Date.now()}_1`,
            type: 'video',
            createdAt: new Date(Date.now() - 1800000).toISOString(),
            status: 'finish',
            positivePrompt: 'A beautiful sunset over the ocean',
            videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
            imageUrl: 'https://picsum.photos/800/600?random=video1',
            quality: 'high',
            duration: 5,
          },
          {
            uuid: `img_mock_${Date.now()}_1`,
            type: 'image',
            createdAt: new Date(Date.now() - 3600000).toISOString(),
            status: 'finish',
            positivePrompt: 'A futuristic cityscape at night',
            imageUrl: 'https://picsum.photos/800/800?random=img1',
            style: 'realistic',
            size: '800x800',
            quality: 'high',
          },
          {
            uuid: `video_mock_${Date.now()}_2`,
            type: 'video',
            createdAt: new Date(Date.now() - 7200000).toISOString(),
            status: 'finish',
            positivePrompt: 'Dancing flowers in the wind',
            videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
            imageUrl: 'https://picsum.photos/800/600?random=video2',
            quality: 'standard',
            duration: 5,
          },
        ];

        // Filter by type if specified
        const filteredMaterials = data.type
          ? mockMaterials.filter((material) => material.type === data.type)
          : mockMaterials;

        resolve({
          success: true,
          list: filteredMaterials,
          hasNextPage: false,
        });
      }, 600);
    });
  }

  return request('/r/Adaptor/MaterialRpcI/listAllMaterials', [data]);
};

// 获取素材统计信息 - Get material statistics
export const getMaterialStats = (): Promise<BaseApiResponse & { stats?: MaterialStats }> => {
  // Mock implementation for development
  if (process.env.NODE_ENV === 'development') {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          stats: {
            totalCount: 25,
            monthlyCount: 8,
            videoCount: 15,
            imageCount: 10,
            successRate: 92.5,
          },
        });
      }, 400);
    });
  }

  return request('/r/Adaptor/MaterialRpcI/getMaterialStats', []);
};

// 删除素材 - Delete material
export const deleteMaterial = (data: {
  uuid: string;
  type: 'video' | 'image';
}): Promise<BaseApiResponse> => {
  return request('/r/Adaptor/MaterialRpcI/deleteMaterial', [data]);
};

// 批量删除素材 - Batch delete materials
export const batchDeleteMaterials = (data: {
  uuids: string[];
  type?: 'video' | 'image';
}): Promise<BaseApiResponse> => {
  return request('/r/Adaptor/MaterialRpcI/batchDeleteMaterials', [data]);
};
