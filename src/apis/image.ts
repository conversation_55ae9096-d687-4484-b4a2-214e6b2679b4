import request from './base';
import { ImageGenerationParams, GenerationResponse, ListResponse, ImageMaterial, BaseApiResponse } from '@/types/aiMaterial';

// 生成图片 - Generate image
export const generateImage = (data: ImageGenerationParams): Promise<GenerationResponse> => {
  // Mock implementation for development
  if (process.env.NODE_ENV === 'development') {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          uuid: `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          taskId: `task_${Date.now()}`
        });
      }, 1000);
    });
  }
  
  return request('/r/Adaptor/MaterialRpcI/generateImage', [data]);
};

// 查看图片生成状态 - Check image generation status
export const checkImageStatus = (data: { uuid: string }): Promise<BaseApiResponse & { 
  status?: string; 
  progress?: number; 
  imageUrl?: string; 
}> => {
  // Mock implementation for development
  if (process.env.NODE_ENV === 'development') {
    return new Promise((resolve) => {
      setTimeout(() => {
        const random = Math.random();
        if (random < 0.3) {
          resolve({
            success: true,
            status: 'processing',
            progress: Math.floor(Math.random() * 80) + 10
          });
        } else {
          resolve({
            success: true,
            status: 'finish',
            progress: 100,
            imageUrl: `https://picsum.photos/800/800?random=${Date.now()}`
          });
        }
      }, 500);
    });
  }
  
  return request('/r/Adaptor/MaterialRpcI/checkImageStatus', [data]);
};

// 获取图片列表 - Get image list
export const listImages = (data: {
  nextUuid?: string;
  pageSize?: number;
} = {}): Promise<ListResponse<ImageMaterial>> => {
  // Mock implementation for development
  if (process.env.NODE_ENV === 'development') {
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockImages: ImageMaterial[] = Array.from({ length: 5 }, (_, index) => ({
          uuid: `img_mock_${Date.now()}_${index}`,
          type: 'image',
          createdAt: new Date(Date.now() - index * 3600000).toISOString(),
          status: 'finish',
          positivePrompt: `Mock image prompt ${index + 1}`,
          negativePrompt: 'low quality, blurry',
          imageUrl: `https://picsum.photos/800/800?random=${Date.now() + index}`,
          style: 'realistic',
          size: '800x800',
          quality: 'high'
        }));
        
        resolve({
          success: true,
          list: mockImages,
          hasNextPage: false
        });
      }, 800);
    });
  }
  
  return request('/r/Adaptor/MaterialRpcI/listImages', [data]);
};

// 图片评分 - Rate image
export const rateImage = (data: {
  uuid: string;
  rating: 'like' | 'dislike';
}): Promise<BaseApiResponse> => {
  return request('/r/Adaptor/MaterialRpcI/rateImage', [data]);
};

// 删除图片 - Remove image
export const removeImage = (data: { uuid: string }): Promise<BaseApiResponse> => {
  return request('/r/Adaptor/MaterialRpcI/removeImage', [data]);
};

// 重新生成图片 - Regenerate image
export const reGenerateImage = (data: { uuid: string }): Promise<GenerationResponse> => {
  return request('/r/Adaptor/MaterialRpcI/reGenerateImage', [data]);
};

// 获取背景模板列表 - Get background templates
export const getBackgroundTemplates = (): Promise<ListResponse<any>> => {
  // Mock implementation for development
  if (process.env.NODE_ENV === 'development') {
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockTemplates = [
          {
            id: 'template_1',
            name: 'Cyberpunk City',
            thumbnailUrl: 'https://picsum.photos/120/80?random=1',
            category: 'urban'
          },
          {
            id: 'template_2', 
            name: 'Abstract Lines',
            thumbnailUrl: 'https://picsum.photos/120/80?random=2',
            category: 'abstract'
          },
          {
            id: 'template_3',
            name: 'Clean White',
            thumbnailUrl: 'https://picsum.photos/120/80?random=3',
            category: 'minimal'
          },
          {
            id: 'template_4',
            name: 'Wooden Texture',
            thumbnailUrl: 'https://picsum.photos/120/80?random=4',
            category: 'texture'
          },
          {
            id: 'template_5',
            name: 'Blue Sky',
            thumbnailUrl: 'https://picsum.photos/120/80?random=5',
            category: 'nature'
          }
        ];
        
        resolve({
          success: true,
          list: mockTemplates
        });
      }, 300);
    });
  }
  
  return request('/r/Adaptor/MaterialRpcI/getBackgroundTemplates', []);
};
