.ai-material-image-uploader {
  position: relative;

  .upload-area {
    border: 1.5px solid #141414;
    border-radius: 16px;
    text-align: center;
    background: #141414;
    height: 202px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 188px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    outline: none; // Remove default focus outline

    .upload-icon {
      font-size: 48px;
      color: rgba(255, 255, 255, 0.5);
      margin-bottom: 16px;
    }

    .upload-text {
      font-size: 16px;
      color: rgba(255, 255, 255, 0.5);
      font-weight: normal;
    }

    &:hover:not(.disabled) {
      border: 1.5px solid #FF0E53;
    }

    &:active:not(.disabled) {
      border: 1.5px solid #FF0E53;
      box-shadow: 0px 4px 10px 0px rgba(255, 14, 83, 0.3);
    }

    // Focus state for accessibility and auto-focus
    &:focus:not(.disabled) {
      border: 1.5px solid #FF0E53;
      box-shadow: 0px 4px 10px 0px rgba(255, 14, 83, 0.3);
    }

    // Focus-visible for better accessibility (only show focus ring when navigating with keyboard)
    &:focus-visible:not(.disabled) {
      border: 1.5px solid #FF0E53;
      box-shadow: 0px 4px 10px 0px rgba(255, 14, 83, 0.3);
    }

    &.disabled {
      cursor: not-allowed;
      opacity: 0.6;
      
      .upload-icon,
      .upload-text {
        color: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .uploaded-image {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    background: #1a1a1a;
    border: 1px solid #333333;

    .preview-img {
      width: 100%;
      height: 200px;
      object-fit: cover;
      display: block;
    }

    .image-actions {
      position: absolute;
      top: 8px;
      right: 8px;
      background: rgba(0, 0, 0, 0.6);
      display: flex;
      gap: 4px;
      opacity: 1;
      border-radius: 4px;
      padding: 4px;

      .action-btn {
        min-width: 32px;
        height: 32px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        font-size: 14px;

        &:hover:not(:disabled) {
          background: rgba(255, 255, 255, 0.2);
          border-color: rgba(255, 255, 255, 0.3);
        }

        &.danger {
          background: rgba(255, 77, 79, 0.2);
          border-color: rgba(255, 77, 79, 0.3);

          &:hover:not(:disabled) {
            background: rgba(255, 77, 79, 0.4);
            border-color: rgba(255, 77, 79, 0.5);
          }
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }

  .upload-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    z-index: 10;

    .loading-spinner {
      width: 32px;
      height: 32px;
      border: 3px solid #333333;
      border-top: 3px solid #ffffff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 12px;
    }

    span {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.5);
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
