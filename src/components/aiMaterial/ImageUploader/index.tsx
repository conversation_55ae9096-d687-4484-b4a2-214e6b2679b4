import { i18next } from '@ali/dingtalk-i18n';
import React, { useState, useEffect, useRef } from 'react';
import { Button, Toast } from 'dingtalk-design-mobile';
import $uploadImage from '@ali/dingtalk-jsapi/api/biz/util/uploadImage';
import { AddToSFilled, ResumePreviewOutlined, DeleteOutlined } from '@ali/ding-icons';
import { previewImages } from '@/components/imagesPreview';
import { isDingTalk, configDingTalkImageUpload } from '@/utils/jsapi';
import './index.less';

interface ImageUploaderProps {
  value?: string | null;
  onChange?: (imageUrl: string | null) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

const ImageUploader: React.FC<ImageUploaderProps> = ({ 
  value, 
  onChange, 
  placeholder,
  disabled = false,
  className = ''
}) => {
  const [loading, setLoading] = useState(false);
  const [apiConfigured, setApiConfigured] = useState(false);
  const uploadAreaRef = useRef<HTMLDivElement>(null);

  // Configure DingTalk JS-API on component mount
  useEffect(() => {
    if (isDingTalk()) {
      configDingTalkImageUpload()
        .then(() => {
          setApiConfigured(true);
        })
        .catch((error) => {
        // eslint-disable-next-line no-console
          console.error('Failed to configure DingTalk JS-API:', error);
          setApiConfigured(false);
        });
    } else {
      // Not in DingTalk environment, disable API features
      setApiConfigured(false);
    }
  }, []);

  const handlePreview = () => {
    if (value) {
      previewImages([value], 0);
    }
  };

  const handleRemove = () => {
    onChange?.(null);
  };

  const handleUploadClick = () => {
    if (disabled || loading) return;
    
    if (!isDingTalk()) {
      Toast.info({
        content: i18next.t('j-dingtalk-web_components_aiMaterial_ImageUploader_PleaseUseInDingTalk'),
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
      return;
    }

    if (!apiConfigured) {
      Toast.fail({
        content: i18next.t('j-dingtalk-web_components_aiMaterial_ImageUploader_ApiNotConfigured'),
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
      return;
    }

    setLoading(true);

    // Use DingTalk biz.util.uploadImage API with .then callback
    $uploadImage({
      multiple: false, // Single image upload
      compression: true, // Enable image compression
      max: 1, // Maximum 1 image
    }).then((result: any) => {
      // Handle upload success
      if (result && result.length > 0) {
        // Get the first uploaded image URL
        const imageUrl = result[0];
        onChange?.(imageUrl);
        Toast.success({
          content: i18next.t('j-dingtalk-web_components_aiMaterial_ImageUploader_ImageUploadedSuccessfully'),
          position: 'top',
          maskClickable: true,
          duration: 2,
        });
      } else {
        Toast.fail({
          content: i18next.t('j-dingtalk-web_components_aiMaterial_ImageUploader_ImageUploadFailed'),
          position: 'top',
          maskClickable: true,
          duration: 3,
        });
      }
      setLoading(false);
    }).catch((error: any) => {
      // Handle upload failure
      console.error(error);
      setLoading(false);
    });
  };

  const defaultPlaceholder = placeholder || i18next.t('j-dingtalk-web_components_aiMaterial_ImageUploader_ClickUploadImage');

  return (
    <div className={`ai-material-image-uploader ${className}`}>
      {value ?
        <div className="uploaded-image">
          <img src={value} alt="Uploaded" className="preview-img" />
          <div className="image-actions">
            <Button
              size="small"
              onClick={handlePreview}
              className="action-btn preview-btn"
              disabled={disabled}
            >
              <ResumePreviewOutlined />
            </Button>
            <Button
              size="small"
              onClick={handleRemove}
              className="action-btn remove-btn danger"
              disabled={disabled}
            >
              <DeleteOutlined />
            </Button>
          </div>
        </div> :

        <div
          ref={uploadAreaRef}
          className={`upload-area ${disabled ? 'disabled' : ''}`}
          onClick={handleUploadClick}
          tabIndex={disabled ? -1 : 0}
          role="button"
          aria-label={defaultPlaceholder}
          onKeyDown={(e) => {
            // Handle Enter and Space key for accessibility
            if (!disabled && (e.key === 'Enter' || e.key === ' ')) {
              e.preventDefault();
              handleUploadClick();
            }
          }}
        >
          <AddToSFilled className="upload-icon" />
          <div className="upload-text">{defaultPlaceholder}</div>
        </div>
      }

      {loading &&
      <div className="upload-loading">
        <div className="loading-spinner" />
        <span>{i18next.t('j-dingtalk-web_components_aiMaterial_ImageUploader_Uploading')}</span>
      </div>
      }
    </div>);
};

export default ImageUploader;
