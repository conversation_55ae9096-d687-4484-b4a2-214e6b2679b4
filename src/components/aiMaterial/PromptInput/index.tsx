import { i18next } from '@ali/dingtalk-i18n';
import React, { useState, useRef, useEffect } from 'react';
import { TextareaItem } from 'dingtalk-design-mobile';
import './index.less';

interface PromptInputProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  maxLength?: number;
  disabled?: boolean;
  className?: string;
  label?: string;
  required?: boolean;
  showWordCount?: boolean;
  rows?: number;
}

const PromptInput: React.FC<PromptInputProps> = ({
  value = '',
  onChange,
  placeholder,
  maxLength = 500,
  disabled = false,
  className = '',
  label,
  required = false,
  showWordCount = true,
  rows = 4
}) => {
  const [focused, setFocused] = useState(false);
  const textAreaRef = useRef<HTMLTextAreaElement>(null);

  const handleChange = (val: string) => {
    onChange?.(val);
  };

  const handleFocus = () => {
    setFocused(true);
  };

  const handleBlur = () => {
    setFocused(false);
  };

  const defaultPlaceholder = placeholder || i18next.t('j-dingtalk-web_components_aiMaterial_PromptInput_EnterYourCreativeDescription');

  return (
    <div className={`ai-material-prompt-input ${className} ${focused ? 'focused' : ''}`}>
      {label && (
        <div className="prompt-label">
          {label}
          {required && <span className="required-mark">*</span>}
        </div>
      )}
      
      <div className="prompt-input-wrapper">
        <TextareaItem
          value={value}
          onChange={handleChange}
          placeholder={defaultPlaceholder}
          maxLength={maxLength}
          disabled={disabled}
          rows={rows}
          onFocus={handleFocus}
          onBlur={handleBlur}
          className="prompt-textarea"
          count={maxLength}
        />
        
        {showWordCount && (
          <div className="word-count">
            <span className={value.length > maxLength * 0.9 ? 'warning' : ''}>
              {value.length}
            </span>
            <span className="separator">/</span>
            <span className="max-length">{maxLength}</span>
          </div>
        )}
      </div>
      
      {/* Helper text or tips */}
      <div className="prompt-tips">
        {i18next.t('j-dingtalk-web_components_aiMaterial_PromptInput_TipDescribeYourIdea')}
      </div>
    </div>
  );
};

export default PromptInput;
