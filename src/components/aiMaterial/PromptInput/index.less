.ai-material-prompt-input {
  .prompt-label {
    font-size: 16px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 4px;

    .required-mark {
      color: #FF0E53;
      font-size: 16px;
    }
  }

  .prompt-input-wrapper {
    position: relative;
    border-radius: 12px;
    background: #1a1a1a;
    border: 1px solid #333333;
    transition: all 0.3s ease;

    &:hover {
      border-color: #555555;
    }

    .prompt-textarea {
      background: transparent !important;
      border: none !important;
      color: rgba(255, 255, 255, 0.9) !important;
      font-size: 14px;
      line-height: 1.5;
      padding: 16px;
      resize: none;
      width: 100%;
      box-sizing: border-box;

      &::placeholder {
        color: rgba(255, 255, 255, 0.4);
      }

      &:focus {
        outline: none;
        box-shadow: none;
      }

      &:disabled {
        background: rgba(255, 255, 255, 0.05) !important;
        color: rgba(255, 255, 255, 0.3) !important;
        cursor: not-allowed;
      }
    }

    .word-count {
      position: absolute;
      bottom: 8px;
      right: 12px;
      font-size: 12px;
      color: rgba(255, 255, 255, 0.4);
      display: flex;
      align-items: center;
      gap: 2px;
      background: rgba(0, 0, 0, 0.6);
      padding: 4px 8px;
      border-radius: 6px;

      .warning {
        color: #FF6B35;
      }

      .separator {
        color: rgba(255, 255, 255, 0.3);
      }

      .max-length {
        color: rgba(255, 255, 255, 0.3);
      }
    }
  }

  &.focused .prompt-input-wrapper {
    border-color: #FF0E53;
    box-shadow: 0px 0px 0px 2px rgba(255, 14, 83, 0.1);
  }

  .prompt-tips {
    margin-top: 8px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.4);
    line-height: 1.4;
  }

  // Mobile specific styles
  @media (max-width: 768px) {
    .prompt-label {
      font-size: 14px;
      margin-bottom: 8px;
    }

    .prompt-input-wrapper {
      .prompt-textarea {
        font-size: 16px; // Prevent zoom on iOS
        padding: 12px;
      }

      .word-count {
        bottom: 6px;
        right: 8px;
        font-size: 11px;
        padding: 2px 6px;
      }
    }

    .prompt-tips {
      font-size: 11px;
      margin-top: 6px;
    }
  }
}
