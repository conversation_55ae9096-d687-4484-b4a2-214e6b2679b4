import { i18next } from '@ali/dingtalk-i18n';
import React, { useEffect, useState, useRef } from 'react';
import { Toast } from 'dingtalk-design-mobile';
import theme, { IThemeType } from 'dingtalk-theme';
import { isDingTalk, isPc, isMobileDevice, setPageTitle } from '@/utils/jsapi';
import { sendUT } from '@/utils/trace';
import { generateImage, checkImageStatus, listImages } from '@/apis/image';
import $setRight from '@ali/dingtalk-jsapi/api/biz/navigation/setRight';
import $setShare from '@ali/dingtalk-jsapi/api/biz/util/share';
import Loading from '@/components/Loading';
import WelcomeScreen from './components/WelcomeScreen';
import ImageForm from './components/ImageForm';
import ImageList from './components/ImageList';
import { ImageMaterial } from '@/types/aiMaterial';
import './index.less';

interface AIImageState {
  currentStep: 'welcome' | 'form' | 'imageList';
  rightPanelContent: 'welcome' | 'imageList'; // For PC layout right panel
  uploadedImage: string | null;
  positivePrompt: string;
  negativePrompt: string;
  selectedSize: string;
  selectedTemplate: string | null;
  isGenerating: boolean;
  generatedImageUrl: string | null;
  error: string | null;
  taskId: string | null;
  progress: number;
  // Image list related states
  imageList: ImageMaterial[];
  isLoadingImages: boolean;
  hasImages: boolean;
  imageListError: string | null;
  nextUuid: string | null;
  hasNextPage: boolean;
  progressMap: Record<string, number>; // Map of image UUID to progress percentage
  isLoadingMore: boolean; // Loading state for pagination
}

const AIImagePage: React.FC = () => {
  const [state, setState] = useState<AIImageState>({
    currentStep: isMobileDevice() ? 'welcome' : 'form',
    rightPanelContent: 'welcome', // PC layout right panel starts with welcome
    uploadedImage: null,
    positivePrompt: '',
    negativePrompt: '',
    selectedSize: '800x800',
    selectedTemplate: null,
    isGenerating: false,
    generatedImageUrl: null,
    error: null,
    taskId: '',
    progress: 0,
    // Image list related states
    imageList: [],
    isLoadingImages: true, // Start with loading state
    hasImages: false,
    imageListError: null,
    nextUuid: null,
    hasNextPage: false,
    progressMap: {},
    isLoadingMore: false, // Loading state for pagination
  });

  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const pollingUuidsRef = useRef<Set<string>>(new Set());

  // Initialize page
  useEffect(() => {
    // Set page title
    setPageTitle(i18next.t('j-dingtalk-web_pages_aiImage_PageTitle'));

    // Apply theme
    const currentTheme: IThemeType = theme.getTheme();
    theme.applyTheme(currentTheme);

    // Set navigation for mobile
    if (isDingTalk() && isMobileDevice()) {
      $setRight({
        show: false
      });
    }

    // Load initial data
    loadImageList();

    // Send page view event
    sendUT('ai_image_page_view', {
      device: isMobileDevice() ? 'mobile' : 'pc'
    });

    // Cleanup on unmount
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
    };
  }, []);

  const loadImageList = async () => {
    try {
      setState(prev => ({ ...prev, isLoadingImages: true, imageListError: null }));

      const result = await listImages({ pageSize: 20 });

      if (result.success && result.list) {
        const imageList = result.list;
        const hasImages = imageList.length > 0;
        const isMobile = isMobileDevice();

        // Calculate initial progress for processing images
        const initialProgressMap: Record<string, number> = {};
        imageList.forEach((image) => {
          if (image.status === 'processing' && image.createdAt) {
            initialProgressMap[image.uuid] = calculateTimeBasedProgress(image.createdAt);
          }
        });

        // Determine initial step based on device and image availability
        let initialStep: AIImageState['currentStep'];
        if (isMobile) {
          initialStep = hasImages ? 'imageList' : 'welcome';
        } else {
          initialStep = 'form';
        }

        setState(prev => ({
          ...prev,
          imageList,
          hasImages,
          isLoadingImages: false,
          nextUuid: result.nextUuid || null,
          hasNextPage: result.hasNextPage || false,
          currentStep: initialStep,
          rightPanelContent: hasImages ? 'imageList' : 'welcome',
          progressMap: initialProgressMap
        }));

        // Start polling for images that need it
        checkAndStartPollingForImages(imageList);
      } else {
        // Handle empty image list
        setState(prev => ({
          ...prev,
          imageList: [],
          hasImages: false,
          isLoadingImages: false,
          nextUuid: null,
          hasNextPage: false,
          currentStep: isMobileDevice() ? 'welcome' : 'form',
          rightPanelContent: 'welcome',
        }));
      }
    } catch (error) {
      // Handle API error silently, log for debugging if needed
      setState(prev => ({
        ...prev,
        isLoadingImages: false,
        imageListError: error instanceof Error ? error.message : i18next.t('j-dingtalk-web_pages_aiImage_FailedToObtainTheImage'),
        hasImages: false,
        // Keep original welcome/form logic when API fails
        currentStep: isMobileDevice() ? 'welcome' : 'form',
        rightPanelContent: 'welcome',
      }));
    }
  };

  const calculateTimeBasedProgress = (createdAt: string): number => {
    const now = Date.now();
    const created = new Date(createdAt).getTime();
    const elapsed = now - created;
    const estimatedDuration = 60000; // 60 seconds estimated generation time
    
    const progress = Math.min(90, Math.floor((elapsed / estimatedDuration) * 100));
    return Math.max(10, progress);
  };

  const checkAndStartPollingForImages = (images: ImageMaterial[]) => {
    const processingImages = images.filter(img => 
      img.status === 'processing' || img.status === 'pending'
    );

    if (processingImages.length > 0) {
      processingImages.forEach(img => {
        pollingUuidsRef.current.add(img.uuid);
      });
      startPollingForImages();
    }
  };

  const startPollingForImages = () => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
    }

    pollingIntervalRef.current = setInterval(async () => {
      const uuidsToCheck = Array.from(pollingUuidsRef.current);
      
      if (uuidsToCheck.length === 0) {
        clearInterval(pollingIntervalRef.current!);
        pollingIntervalRef.current = null;
        return;
      }

      for (const uuid of uuidsToCheck) {
        try {
          const result = await checkImageStatus({ uuid });
          
          if (result.success) {
            if (result.status === 'finish') {
              // Remove from polling
              pollingUuidsRef.current.delete(uuid);
              
              // Update image list
              setState(prev => ({
                ...prev,
                imageList: prev.imageList.map(img => 
                  img.uuid === uuid 
                    ? { ...img, status: 'finish', imageUrl: result.imageUrl || img.imageUrl }
                    : img
                ),
                progressMap: {
                  ...prev.progressMap,
                  [uuid]: 100
                }
              }));
            } else if (result.status === 'failed') {
              // Remove from polling
              pollingUuidsRef.current.delete(uuid);
              
              // Update image status
              setState(prev => ({
                ...prev,
                imageList: prev.imageList.map(img => 
                  img.uuid === uuid ? { ...img, status: 'failed' } : img
                ),
                progressMap: {
                  ...prev.progressMap,
                  [uuid]: 0
                }
              }));
            } else if (result.status === 'processing' && result.progress) {
              // Update progress
              setState(prev => ({
                ...prev,
                progressMap: {
                  ...prev.progressMap,
                  [uuid]: result.progress || calculateTimeBasedProgress(
                    prev.imageList.find(img => img.uuid === uuid)?.createdAt || new Date().toISOString()
                  )
                }
              }));
            }
          }
        } catch (error) {
          console.error(`Failed to check status for image ${uuid}:`, error);
        }
      }
    }, 3000); // Poll every 3 seconds
  };

  // Handle form updates
  const handleFormUpdate = (updates: Partial<AIImageState>) => {
    setState(prev => ({ ...prev, ...updates }));
  };

  // Handle step changes
  const handleStepChange = (step: AIImageState['currentStep']) => {
    setState(prev => ({ ...prev, currentStep: step }));
    
    sendUT('ai_image_step_change', {
      from: state.currentStep,
      to: step,
      device: isMobileDevice() ? 'mobile' : 'pc'
    });
  };

  // Handle image generation
  const handleGenerateImage = async () => {
    if (!state.positivePrompt) {
      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_aiImage_PleaseEnterADescription'),
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
      return;
    }

    setState((prev) => ({
      ...prev,
      isGenerating: true,
      error: null,
      progress: 0,
      generatedImageUrl: null,
    }));

    try {
      // Send generate event
      sendUT('ai_image_generate_click', {
        device: isMobileDevice() ? 'mobile' : 'pc',
        size: state.selectedSize,
        positivePrompt: state.positivePrompt,
        negativePrompt: state.negativePrompt,
        originalImage: state.uploadedImage,
        templateId: state.selectedTemplate,
      });

      // Call image generation API
      const result = await generateImage({
        prompt: state.positivePrompt,
        negativePrompt: state.negativePrompt,
        style: 'realistic',
        size: state.selectedSize,
        quality: 'high',
        originalImageUrl: state.uploadedImage || undefined,
        templateId: state.selectedTemplate || undefined,
      });

      if (result.success && result.uuid) {
        setState((prev) => ({
          ...prev,
          isGenerating: false,
          taskId: result.uuid,
          currentStep: 'imageList',
          rightPanelContent: 'imageList',
        }));

        // Refresh image list to include the new generation
        loadImageList();

        // Start polling for the new image
        if (result.uuid) {
          pollingUuidsRef.current.add(result.uuid);
          startPollingForImages();
        }
      } else {
        throw new Error(result?.errorMsg || i18next.t('j-dingtalk-web_pages_aiImage_FailedToGenerate'));
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        isGenerating: false,
        error: error instanceof Error ? error.message : i18next.t('j-dingtalk-web_pages_aiImage_FailedToGeneratePleaseTry'),
        progress: 0,
      }));
      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_aiImage_ImageGenerationFailedPleaseTry'),
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
    }
  };

  // Render PC layout with left-right structure
  const renderPCLayout = () => {
    // Show loading overlay if images are loading
    if (state.isLoadingImages) {
      return (
        <div className="ai-image-pc-layout">
          <Loading text={i18next.t('j-dingtalk-web_pages_aiImage_Loading')} />
        </div>);
    }

    return (
      <div className="ai-image-pc-layout">
        {/* Left Panel - Always show ImageForm */}
        <div className="ai-image-left-panel">
          <ImageForm
            state={state}
            onUpdate={handleFormUpdate}
            onGenerate={handleGenerateImage}
          />
        </div>

        {/* Right Panel - Show welcome or image list */}
        <div className="ai-image-right-panel">
          {state.rightPanelContent === 'welcome' && (
            <WelcomeScreen
              hasGenerateBtn={false}
              onGetStarted={() => {}}
            />
          )}

          {state.rightPanelContent === 'imageList' && (
            <ImageList
              imageList={state.imageList}
              isLoading={false} // Loading is handled at page level
              error={state.imageListError}
              onCreateNew={() => handleStepChange('form')}
              onRefresh={loadImageList}
              progressMap={state.progressMap}
              hasNextPage={state.hasNextPage}
              isLoadingMore={state.isLoadingMore}
              onLoadMore={() => {}} // TODO: Implement load more
            />
          )}
        </div>
      </div>);
  };

  // Render mobile layout
  const renderMobileLayout = () => {
    // Show loading screen if images are loading
    if (state.isLoadingImages) {
      return <Loading text={i18next.t('j-dingtalk-web_pages_aiImage_Loading')} />;
    }

    switch (state.currentStep) {
      case 'welcome':
        return (
          <WelcomeScreen
            hasGenerateBtn
            onGetStarted={() => handleStepChange('form')}
          />);
      case 'form':
        return (
          <ImageForm
            state={state}
            onUpdate={handleFormUpdate}
            onGenerate={handleGenerateImage}
          />);
      case 'imageList':
        return (
          <ImageList
            imageList={state.imageList}
            isLoading={false} // Loading is handled at page level
            error={state.imageListError}
            onCreateNew={() => handleStepChange('form')}
            onRefresh={loadImageList}
            progressMap={state.progressMap}
            hasNextPage={state.hasNextPage}
            isLoadingMore={state.isLoadingMore}
            onLoadMore={() => {}} // TODO: Implement load more
          />);
      default:
        return null;
    }
  };

  return (
    <div className="ai-image-page">
      {isPc() ? renderPCLayout() : renderMobileLayout()}
    </div>
  );
};

export default AIImagePage;
