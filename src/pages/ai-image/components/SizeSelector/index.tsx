import { i18next } from '@ali/dingtalk-i18n';
import React from 'react';
import { DownArrowOutlined } from '@ali/ding-icons';
import './index.less';

interface SizeOption {
  label: string;
  value: string;
}

interface SizeSelectorProps {
  options: SizeOption[];
  selectedSize: string;
  onSizeChange: (size: string) => void;
  disabled?: boolean;
  className?: string;
}

const SizeSelector: React.FC<SizeSelectorProps> = ({
  options,
  selectedSize,
  onSizeChange,
  disabled = false,
  className = ''
}) => {
  const [isOpen, setIsOpen] = React.useState(false);

  const handleToggle = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  const handleSelect = (size: string) => {
    if (!disabled) {
      onSizeChange(size);
      setIsOpen(false);
    }
  };

  const selectedOption = options.find(option => option.value === selectedSize);

  return (
    <div className={`size-selector ${className} ${disabled ? 'disabled' : ''}`}>
      <div 
        className={`selector-trigger ${isOpen ? 'open' : ''}`}
        onClick={handleToggle}
        role="button"
        tabIndex={disabled ? -1 : 0}
        onKeyDown={(e) => {
          if (!disabled && (e.key === 'Enter' || e.key === ' ')) {
            e.preventDefault();
            handleToggle();
          }
        }}
      >
        <span className="selected-value">
          {selectedOption?.label || selectedSize}
        </span>
        <DownArrowOutlined className={`arrow-icon ${isOpen ? 'rotated' : ''}`} />
      </div>

      {isOpen && (
        <div className="selector-dropdown">
          {options.map((option) => (
            <div
              key={option.value}
              className={`dropdown-item ${option.value === selectedSize ? 'selected' : ''}`}
              onClick={() => handleSelect(option.value)}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  handleSelect(option.value);
                }
              }}
            >
              <span className="option-label">{option.label}</span>
              {option.value === selectedSize && (
                <span className="check-mark">✓</span>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Overlay to close dropdown when clicking outside */}
      {isOpen && (
        <div 
          className="selector-overlay" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default SizeSelector;
