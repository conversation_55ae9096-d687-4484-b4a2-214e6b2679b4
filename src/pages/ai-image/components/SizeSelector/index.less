.size-selector {
  position: relative;

  .selector-trigger {
    background: #1a1a1a;
    border: 1px solid #333333;
    border-radius: 12px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 48px;

    &:hover:not(.disabled) {
      border-color: #555555;
      background: #222222;
    }

    &.open {
      border-color: #667eea;
      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
    }

    .selected-value {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.9);
      font-weight: 500;
    }

    .arrow-icon {
      font-size: 12px;
      color: rgba(255, 255, 255, 0.6);
      transition: transform 0.3s ease;

      &.rotated {
        transform: rotate(180deg);
      }
    }
  }

  .selector-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #1a1a1a;
    border: 1px solid #333333;
    border-radius: 12px;
    margin-top: 4px;
    z-index: 1000;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
    overflow: hidden;

    .dropdown-item {
      padding: 12px 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
      transition: background-color 0.2s ease;
      border-bottom: 1px solid rgba(255, 255, 255, 0.05);

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background: rgba(255, 255, 255, 0.05);
      }

      &.selected {
        background: rgba(102, 126, 234, 0.1);
        color: #667eea;
      }

      .option-label {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.9);
      }

      .check-mark {
        font-size: 12px;
        color: #667eea;
        font-weight: bold;
      }

      &.selected .option-label {
        color: #667eea;
        font-weight: 500;
      }
    }
  }

  .selector-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    background: transparent;
  }

  &.disabled {
    .selector-trigger {
      background: rgba(255, 255, 255, 0.05);
      border-color: rgba(255, 255, 255, 0.1);
      cursor: not-allowed;

      .selected-value {
        color: rgba(255, 255, 255, 0.4);
      }

      .arrow-icon {
        color: rgba(255, 255, 255, 0.3);
      }
    }
  }

  // Mobile specific styles
  @media (max-width: 768px) {
    .selector-trigger {
      padding: 10px 14px;
      min-height: 44px;

      .selected-value {
        font-size: 13px;
      }

      .arrow-icon {
        font-size: 11px;
      }
    }

    .selector-dropdown {
      .dropdown-item {
        padding: 10px 14px;

        .option-label {
          font-size: 13px;
        }

        .check-mark {
          font-size: 11px;
        }
      }
    }
  }

  // PC specific styles
  @media (min-width: 769px) {
    .selector-trigger {
      padding: 14px 18px;
      min-height: 52px;

      .selected-value {
        font-size: 15px;
      }

      .arrow-icon {
        font-size: 13px;
      }
    }

    .selector-dropdown {
      .dropdown-item {
        padding: 14px 18px;

        .option-label {
          font-size: 15px;
        }

        .check-mark {
          font-size: 13px;
        }
      }
    }
  }
}
