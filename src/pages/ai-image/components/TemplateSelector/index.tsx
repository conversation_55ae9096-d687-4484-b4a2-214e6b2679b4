import { i18next } from '@ali/dingtalk-i18n';
import React, { useRef, useEffect } from 'react';
import { BackgroundTemplate } from '@/types/aiMaterial';
import './index.less';

interface TemplateSelectorProps {
  templates: BackgroundTemplate[];
  selectedTemplate: string | null;
  onTemplateChange: (templateId: string | null) => void;
  loading?: boolean;
  disabled?: boolean;
  className?: string;
}

const TemplateSelector: React.FC<TemplateSelectorProps> = ({
  templates,
  selectedTemplate,
  onTemplateChange,
  loading = false,
  disabled = false,
  className = ''
}) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to selected template
  useEffect(() => {
    if (selectedTemplate && scrollContainerRef.current) {
      const selectedElement = scrollContainerRef.current.querySelector(
        `[data-template-id="${selectedTemplate}"]`
      );
      if (selectedElement) {
        selectedElement.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'center'
        });
      }
    }
  }, [selectedTemplate]);

  const handleTemplateSelect = (templateId: string) => {
    if (!disabled) {
      // Toggle selection - if already selected, deselect
      if (selectedTemplate === templateId) {
        onTemplateChange(null);
      } else {
        onTemplateChange(templateId);
      }
    }
  };

  const renderLoadingSkeleton = () => (
    <div className="template-loading">
      {Array.from({ length: 5 }, (_, index) => (
        <div key={index} className="template-skeleton">
          <div className="skeleton-image" />
        </div>
      ))}
    </div>
  );

  const renderTemplateItem = (template: BackgroundTemplate) => {
    const isSelected = selectedTemplate === template.id;
    
    return (
      <div
        key={template.id}
        data-template-id={template.id}
        className={`template-item ${isSelected ? 'selected' : ''} ${disabled ? 'disabled' : ''}`}
        onClick={() => handleTemplateSelect(template.id)}
        role="button"
        tabIndex={disabled ? -1 : 0}
        onKeyDown={(e) => {
          if (!disabled && (e.key === 'Enter' || e.key === ' ')) {
            e.preventDefault();
            handleTemplateSelect(template.id);
          }
        }}
      >
        <div className="template-image-wrapper">
          <img 
            src={template.thumbnailUrl} 
            alt={template.name}
            className="template-image"
            loading="lazy"
          />
          {isSelected && (
            <div className="selection-overlay">
              <div className="check-mark">✓</div>
            </div>
          )}
        </div>
        <div className="template-name">{template.name}</div>
      </div>
    );
  };

  return (
    <div className={`template-selector ${className} ${disabled ? 'disabled' : ''}`}>
      {loading ? (
        renderLoadingSkeleton()
      ) : (
        <div 
          ref={scrollContainerRef}
          className="templates-container"
        >
          {/* None option */}
          <div
            className={`template-item none-template ${selectedTemplate === null ? 'selected' : ''} ${disabled ? 'disabled' : ''}`}
            onClick={() => !disabled && onTemplateChange(null)}
            role="button"
            tabIndex={disabled ? -1 : 0}
            onKeyDown={(e) => {
              if (!disabled && (e.key === 'Enter' || e.key === ' ')) {
                e.preventDefault();
                onTemplateChange(null);
              }
            }}
          >
            <div className="template-image-wrapper">
              <div className="none-template-placeholder">
                <span className="none-icon">∅</span>
              </div>
              {selectedTemplate === null && (
                <div className="selection-overlay">
                  <div className="check-mark">✓</div>
                </div>
              )}
            </div>
            <div className="template-name">
              {i18next.t('j-dingtalk-web_pages_aiImage_components_TemplateSelector_None')}
            </div>
          </div>

          {/* Template options */}
          {templates.map(renderTemplateItem)}
        </div>
      )}

      {templates.length === 0 && !loading && (
        <div className="empty-templates">
          <div className="empty-icon">🎨</div>
          <div className="empty-text">
            {i18next.t('j-dingtalk-web_pages_aiImage_components_TemplateSelector_NoTemplatesAvailable')}
          </div>
        </div>
      )}
    </div>
  );
};

export default TemplateSelector;
