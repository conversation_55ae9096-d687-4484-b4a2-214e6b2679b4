.template-selector {
  .templates-container {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    padding: 4px;
    scroll-behavior: smooth;

    // Custom scrollbar
    &::-webkit-scrollbar {
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 3px;

      &:hover {
        background: rgba(255, 255, 255, 0.4);
      }
    }
  }

  .template-item {
    flex-shrink: 0;
    width: 80px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 4px;

    &:hover:not(.disabled) {
      transform: translateY(-2px);
    }

    &.selected {
      .template-image-wrapper {
        border-color: #667eea;
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
      }
    }

    &.disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }

    .template-image-wrapper {
      position: relative;
      width: 100%;
      height: 60px;
      border: 2px solid rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      overflow: hidden;
      transition: all 0.3s ease;

      .template-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
      }

      .selection-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(102, 126, 234, 0.3);
        display: flex;
        align-items: center;
        justify-content: center;

        .check-mark {
          width: 20px;
          height: 20px;
          background: #667eea;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: bold;
        }
      }
    }

    .template-name {
      margin-top: 6px;
      font-size: 11px;
      color: rgba(255, 255, 255, 0.8);
      text-align: center;
      line-height: 1.2;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    // None template specific styles
    &.none-template {
      .none-template-placeholder {
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;

        .none-icon {
          font-size: 24px;
          color: rgba(255, 255, 255, 0.5);
        }
      }
    }
  }

  .template-loading {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    padding: 4px;

    .template-skeleton {
      flex-shrink: 0;
      width: 80px;

      .skeleton-image {
        width: 100%;
        height: 60px;
        background: linear-gradient(
          90deg,
          rgba(255, 255, 255, 0.1) 0%,
          rgba(255, 255, 255, 0.2) 50%,
          rgba(255, 255, 255, 0.1) 100%
        );
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        border-radius: 8px;
      }
    }
  }

  .empty-templates {
    text-align: center;
    padding: 40px 20px;
    color: rgba(255, 255, 255, 0.6);

    .empty-icon {
      font-size: 32px;
      margin-bottom: 12px;
    }

    .empty-text {
      font-size: 14px;
      line-height: 1.4;
    }
  }

  &.disabled {
    .template-item {
      cursor: not-allowed;
      opacity: 0.5;

      &:hover {
        transform: none;
      }
    }
  }

  // Mobile specific styles
  @media (max-width: 768px) {
    .templates-container {
      gap: 10px;
      padding: 2px;
    }

    .template-item {
      width: 70px;
      padding: 2px;

      .template-image-wrapper {
        height: 50px;
        border-width: 1.5px;

        .selection-overlay .check-mark {
          width: 16px;
          height: 16px;
          font-size: 10px;
        }
      }

      .template-name {
        margin-top: 4px;
        font-size: 10px;
      }

      &.none-template .none-template-placeholder .none-icon {
        font-size: 20px;
      }
    }

    .template-loading {
      gap: 10px;

      .template-skeleton {
        width: 70px;

        .skeleton-image {
          height: 50px;
        }
      }
    }

    .empty-templates {
      padding: 30px 15px;

      .empty-icon {
        font-size: 28px;
        margin-bottom: 10px;
      }

      .empty-text {
        font-size: 13px;
      }
    }
  }

  // PC specific styles
  @media (min-width: 769px) {
    .templates-container {
      gap: 16px;
      padding: 6px;
    }

    .template-item {
      width: 90px;
      padding: 6px;

      .template-image-wrapper {
        height: 70px;

        .selection-overlay .check-mark {
          width: 24px;
          height: 24px;
          font-size: 14px;
        }
      }

      .template-name {
        margin-top: 8px;
        font-size: 12px;
      }

      &.none-template .none-template-placeholder .none-icon {
        font-size: 28px;
      }
    }

    .template-loading {
      gap: 16px;

      .template-skeleton {
        width: 90px;

        .skeleton-image {
          height: 70px;
        }
      }
    }

    .empty-templates {
      padding: 50px 25px;

      .empty-icon {
        font-size: 36px;
        margin-bottom: 16px;
      }

      .empty-text {
        font-size: 15px;
      }
    }
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
