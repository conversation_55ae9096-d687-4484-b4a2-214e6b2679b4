import { i18next } from '@ali/dingtalk-i18n';
import React from 'react';
import { Button } from 'dingtalk-design-mobile';
import { RefreshOutlined, EditOutlined } from '@ali/ding-icons';
import { isMobileDevice } from '@/utils/jsapi';
import { ImageMaterial } from '@/types/aiMaterial';
import ImageItem from '../ImageItem';
import './index.less';

interface ImageListProps {
  imageList: ImageMaterial[];
  isLoading: boolean;
  error: string | null;
  onCreateNew: () => void;
  onRefresh: () => void;
  progressMap: Record<string, number>;
  hasNextPage: boolean;
  isLoadingMore: boolean;
  onLoadMore: () => void;
}

const ImageList: React.FC<ImageListProps> = ({
  imageList,
  isLoading,
  error,
  onCreateNew,
  onRefresh,
  progressMap,
  hasNextPage,
  isLoadingMore,
  onLoadMore
}) => {
  const isMobile = isMobileDevice();

  const renderHeader = () => (
    <div className="image-list-header">
      <div className="header-left">
        <h2 className="list-title">
          {i18next.t('j-dingtalk-web_pages_aiImage_components_ImageList_MyImages')}
        </h2>
        <span className="image-count">
          {imageList.length} {i18next.t('j-dingtalk-web_pages_aiImage_components_ImageList_Items')}
        </span>
      </div>
      <div className="header-actions">
        <Button
          size="small"
          onClick={onRefresh}
          className="refresh-btn"
          disabled={isLoading}
        >
          <RefreshOutlined />
        </Button>
        <Button
          type="primary"
          size="small"
          onClick={onCreateNew}
          className="create-btn"
        >
          <EditOutlined />
          {!isMobile && i18next.t('j-dingtalk-web_pages_aiImage_components_ImageList_CreateNew')}
        </Button>
      </div>
    </div>
  );

  const renderEmptyState = () => (
    <div className="empty-state">
      <div className="empty-icon">🎨</div>
      <div className="empty-title">
        {i18next.t('j-dingtalk-web_pages_aiImage_components_ImageList_NoImagesYet')}
      </div>
      <div className="empty-description">
        {i18next.t('j-dingtalk-web_pages_aiImage_components_ImageList_StartCreatingImages')}
      </div>
      <Button
        type="primary"
        onClick={onCreateNew}
        className="empty-create-btn"
      >
        {i18next.t('j-dingtalk-web_pages_aiImage_components_ImageList_CreateFirstImage')}
      </Button>
    </div>
  );

  const renderErrorState = () => (
    <div className="error-state">
      <div className="error-icon">⚠️</div>
      <div className="error-title">
        {i18next.t('j-dingtalk-web_pages_aiImage_components_ImageList_FailedToLoadImages')}
      </div>
      <div className="error-description">{error}</div>
      <Button
        onClick={onRefresh}
        className="retry-btn"
      >
        {i18next.t('j-dingtalk-web_pages_aiImage_components_ImageList_Retry')}
      </Button>
    </div>
  );

  const renderImageGrid = () => (
    <div className="image-grid">
      {imageList.map((image) => (
        <ImageItem
          key={image.uuid}
          image={image}
          progress={progressMap[image.uuid] || 0}
          onRegenerate={() => {}} // TODO: Implement regenerate
          onDelete={() => {}} // TODO: Implement delete
          onRate={() => {}} // TODO: Implement rate
        />
      ))}
    </div>
  );

  const renderLoadMore = () => {
    if (!hasNextPage) return null;

    return (
      <div className="load-more-section">
        <Button
          onClick={onLoadMore}
          loading={isLoadingMore}
          className="load-more-btn"
          disabled={isLoadingMore}
        >
          {isLoadingMore 
            ? i18next.t('j-dingtalk-web_pages_aiImage_components_ImageList_Loading')
            : i18next.t('j-dingtalk-web_pages_aiImage_components_ImageList_LoadMore')
          }
        </Button>
      </div>
    );
  };

  return (
    <div className="image-list">
      {renderHeader()}
      
      <div className="list-content">
        {error ? (
          renderErrorState()
        ) : imageList.length === 0 ? (
          renderEmptyState()
        ) : (
          <>
            {renderImageGrid()}
            {renderLoadMore()}
          </>
        )}
      </div>
    </div>
  );
};

export default ImageList;
