.image-list {
  .image-list-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 0 4px;

    .header-left {
      display: flex;
      align-items: baseline;
      gap: 12px;

      .list-title {
        font-size: 20px;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
        margin: 0;
      }

      .image-count {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.5);
        background: rgba(255, 255, 255, 0.1);
        padding: 2px 8px;
        border-radius: 4px;
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 8px;

      .refresh-btn {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: rgba(255, 255, 255, 0.7);
        border-radius: 8px;
        min-width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover:not(:disabled) {
          background: rgba(255, 255, 255, 0.15);
          border-color: rgba(255, 255, 255, 0.3);
          color: rgba(255, 255, 255, 0.9);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }

      .create-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        border-radius: 8px;
        height: 36px;
        padding: 0 12px;
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 14px;
        font-weight: 500;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
      }
    }
  }

  .list-content {
    min-height: 300px;
  }

  .empty-state,
  .error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;

    .empty-icon,
    .error-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    .empty-title,
    .error-title {
      font-size: 18px;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: 8px;
    }

    .empty-description,
    .error-description {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.6);
      margin-bottom: 24px;
      line-height: 1.5;
      max-width: 300px;
    }

    .empty-create-btn,
    .retry-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      color: white;
      border-radius: 8px;
      height: 40px;
      padding: 0 20px;
      font-size: 14px;
      font-weight: 500;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      }
    }
  }

  .image-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
  }

  .load-more-section {
    display: flex;
    justify-content: center;
    padding: 20px 0;

    .load-more-btn {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: rgba(255, 255, 255, 0.8);
      border-radius: 8px;
      height: 40px;
      padding: 0 24px;
      font-size: 14px;
      font-weight: 500;

      &:hover:not(:disabled) {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
        color: rgba(255, 255, 255, 0.9);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }

  // Mobile specific styles
  @media (max-width: 768px) {
    .image-list-header {
      margin-bottom: 16px;
      flex-direction: column;
      align-items: stretch;
      gap: 12px;

      .header-left {
        justify-content: space-between;

        .list-title {
          font-size: 18px;
        }

        .image-count {
          font-size: 13px;
        }
      }

      .header-actions {
        justify-content: flex-end;

        .refresh-btn {
          min-width: 32px;
          height: 32px;
        }

        .create-btn {
          height: 32px;
          padding: 0 10px;
          font-size: 13px;
          gap: 4px;
        }
      }
    }

    .image-grid {
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
      gap: 16px;
      margin-bottom: 20px;
    }

    .empty-state,
    .error-state {
      padding: 40px 16px;

      .empty-icon,
      .error-icon {
        font-size: 40px;
        margin-bottom: 12px;
      }

      .empty-title,
      .error-title {
        font-size: 16px;
        margin-bottom: 6px;
      }

      .empty-description,
      .error-description {
        font-size: 13px;
        margin-bottom: 20px;
      }

      .empty-create-btn,
      .retry-btn {
        height: 36px;
        font-size: 13px;
      }
    }

    .load-more-section {
      padding: 16px 0;

      .load-more-btn {
        height: 36px;
        font-size: 13px;
      }
    }
  }

  // PC specific styles
  @media (min-width: 769px) {
    .image-grid {
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
      gap: 24px;
      margin-bottom: 28px;
    }

    .header-actions {
      .create-btn {
        height: 40px;
        padding: 0 16px;
        font-size: 15px;
        gap: 8px;
      }
    }
  }
}
