import { i18next } from '@ali/dingtalk-i18n';
import React, { useState, useEffect } from 'react';
import { Button, Toast } from 'dingtalk-design-mobile';
import { isMobileDevice } from '@/utils/jsapi';
import { ImageUploader, PromptInput } from '@/components/aiMaterial';
import SizeSelector from '../SizeSelector';
import TemplateSelector from '../TemplateSelector';
import { getBackgroundTemplates } from '@/apis/image';
import { BackgroundTemplate } from '@/types/aiMaterial';
import './index.less';

interface ImageFormState {
  uploadedImage: string | null;
  positivePrompt: string;
  negativePrompt: string;
  selectedSize: string;
  selectedTemplate: string | null;
  isGenerating: boolean;
}

interface ImageFormProps {
  state: ImageFormState;
  onUpdate: (updates: Partial<ImageFormState>) => void;
  onGenerate: () => void;
}

const ImageForm: React.FC<ImageFormProps> = ({
  state,
  onUpdate,
  onGenerate,
}) => {
  const [templates, setTemplates] = useState<BackgroundTemplate[]>([]);
  const [loadingTemplates, setLoadingTemplates] = useState(false);

  // Load background templates on mount
  useEffect(() => {
    loadBackgroundTemplates();
  }, []);

  const loadBackgroundTemplates = async () => {
    try {
      setLoadingTemplates(true);
      const result = await getBackgroundTemplates();
      
      if (result.success && result.list) {
        setTemplates(result.list);
      }
    } catch (error) {
      console.error('Failed to load background templates:', error);
    } finally {
      setLoadingTemplates(false);
    }
  };

  const handleImageUpload = (imageUrl: string | null) => {
    onUpdate({ uploadedImage: imageUrl });
  };

  const handlePromptChange = (prompt: string) => {
    onUpdate({ positivePrompt: prompt });
  };

  const handleSizeChange = (size: string) => {
    onUpdate({ selectedSize: size });
  };

  const handleTemplateChange = (templateId: string | null) => {
    onUpdate({ selectedTemplate: templateId });
  };

  const handleSubmit = () => {
    if (!state.positivePrompt) {
      Toast.info({
        content: i18next.t('j-dingtalk-web_pages_aiImage_components_ImageForm_PleaseEnterADescription'),
        position: 'top',
        maskClickable: true,
        duration: 2,
      });
      return;
    }

    onGenerate();
  };

  const sizeOptions = [
    { label: '800x800', value: '800x800' },
    { label: '750x1000', value: '750x1000' },
    { label: '1280x720', value: '1280x720' }
  ];

  return (
    <div className="image-form">
      <div className="form-container">
        {/* Original Image Upload Section */}
        <div className="form-section">
          <div className="section-title">
            {i18next.t('j-dingtalk-web_pages_aiImage_components_ImageForm_UploadOriginalImage')}
          </div>
          <ImageUploader
            value={state.uploadedImage}
            onChange={handleImageUpload}
            placeholder={i18next.t('j-dingtalk-web_pages_aiImage_components_ImageForm_ClickToUploadImage')}
            disabled={state.isGenerating}
          />
        </div>

        {/* Creative Description Section */}
        <div className="form-section">
          <div className="section-title">
            {i18next.t('j-dingtalk-web_pages_aiImage_components_ImageForm_YourCreativeDescription')}
          </div>
          <PromptInput
            value={state.positivePrompt}
            onChange={handlePromptChange}
            placeholder={i18next.t('j-dingtalk-web_pages_aiImage_components_ImageForm_SelectTemplateOrDescribe')}
            disabled={state.isGenerating}
            maxLength={500}
            rows={3}
          />
        </div>

        {/* Background Template Section */}
        <div className="form-section">
          <div className="section-title">
            {i18next.t('j-dingtalk-web_pages_aiImage_components_ImageForm_BackgroundTemplate')}
          </div>
          <TemplateSelector
            templates={templates}
            selectedTemplate={state.selectedTemplate}
            onTemplateChange={handleTemplateChange}
            loading={loadingTemplates}
            disabled={state.isGenerating}
          />
        </div>

        {/* Image Size Section */}
        <div className="form-section">
          <div className="section-title">
            {i18next.t('j-dingtalk-web_pages_aiImage_components_ImageForm_ImageSize')}
          </div>
          <SizeSelector
            options={sizeOptions}
            selectedSize={state.selectedSize}
            onSizeChange={handleSizeChange}
            disabled={state.isGenerating}
          />
        </div>

        {/* Generate Button */}
        <div className="form-actions">
          <Button
            type="primary"
            size="large"
            className="generate-btn"
            loading={state.isGenerating}
            onClick={handleSubmit}
            disabled={!state.positivePrompt}
          >
            {state.isGenerating 
              ? i18next.t('j-dingtalk-web_pages_aiImage_components_ImageForm_Generating') 
              : i18next.t('j-dingtalk-web_pages_aiImage_components_ImageForm_GenerateNow')
            }
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ImageForm;
