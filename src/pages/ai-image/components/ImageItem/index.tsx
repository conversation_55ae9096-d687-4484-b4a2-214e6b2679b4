import { i18next } from '@ali/dingtalk-i18n';
import React, { useState } from 'react';
import { Toast, Modal } from 'dingtalk-design-mobile';
import {
  LikeOutlined,
  LikeFilled,
  DislikeOutlined,
  DislikeFilled,
  RefreshOutlined,
  DownloadAndSaveOutlined,
  DeleteOutlined
} from '@ali/ding-icons';
import { ImageMaterial } from '@/types/aiMaterial';
import { formatCompletionTime } from '@/utils/util';
import { previewImages } from '@/components/imagesPreview';
import './index.less';

interface ImageItemProps {
  image: ImageMaterial;
  progress: number;
  onRegenerate: (uuid: string) => void;
  onDelete: (uuid: string) => void;
  onRate: (uuid: string, rating: 'like' | 'dislike') => void;
}

const ImageItem: React.FC<ImageItemProps> = ({
  image,
  progress,
  onRegenerate,
  onDelete,
  onRate
}) => {
  const [showActions, setShowActions] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleImageClick = () => {
    if (image.status === 'finish' && image.imageUrl) {
      previewImages([image.imageUrl], 0);
    }
  };

  const handleRegenerate = (e: React.MouseEvent) => {
    e.stopPropagation();
    onRegenerate(image.uuid);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    
    Modal.confirm({
      title: i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_ConfirmDelete'),
      content: i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_DeleteWarning'),
      onOk: () => {
        setIsDeleting(true);
        onDelete(image.uuid);
      }
    });
  };

  const handleRate = (rating: 'like' | 'dislike') => (e: React.MouseEvent) => {
    e.stopPropagation();
    onRate(image.uuid, rating);
  };

  const handleDownload = (e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (image.status === 'finish' && image.imageUrl) {
      // Create download link
      const link = document.createElement('a');
      link.href = image.imageUrl;
      link.download = `ai-image-${image.uuid}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      Toast.success({
        content: i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_DownloadStarted'),
        position: 'top',
        duration: 2
      });
    }
  };

  const renderStatusOverlay = () => {
    if (image.status === 'processing' || image.status === 'pending') {
      return (
        <div className="status-overlay processing">
          <div className="progress-circle">
            <svg width="40" height="40" viewBox="0 0 40 40">
              <circle
                cx="20"
                cy="20"
                r="16"
                fill="none"
                stroke="rgba(255, 255, 255, 0.3)"
                strokeWidth="2"
              />
              <circle
                cx="20"
                cy="20"
                r="16"
                fill="none"
                stroke="white"
                strokeWidth="2"
                strokeDasharray={`${2 * Math.PI * 16}`}
                strokeDashoffset={`${2 * Math.PI * 16 * (1 - progress / 100)}`}
                strokeLinecap="round"
                transform="rotate(-90 20 20)"
              />
            </svg>
            <span className="progress-text">{progress}%</span>
          </div>
          <div className="status-text">
            {i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_Generating')}
          </div>
        </div>
      );
    }

    if (image.status === 'failed') {
      return (
        <div className="status-overlay failed">
          <div className="failed-icon">⚠️</div>
          <div className="status-text">
            {i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_GenerationFailed')}
          </div>
        </div>
      );
    }

    return null;
  };

  const renderActionButtons = () => {
    if (image.status !== 'finish') return null;

    return (
      <div className="action-buttons">
        <div className="action-row">
          <button
            className={`action-btn rate-btn ${image.userRating === 'like' ? 'active' : ''}`}
            onClick={handleRate('like')}
            title={i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_Like')}
          >
            {image.userRating === 'like' ? <LikeFilled /> : <LikeOutlined />}
          </button>
          
          <button
            className={`action-btn rate-btn ${image.userRating === 'dislike' ? 'active' : ''}`}
            onClick={handleRate('dislike')}
            title={i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_Dislike')}
          >
            {image.userRating === 'dislike' ? <DislikeFilled /> : <DislikeOutlined />}
          </button>

          <button
            className="action-btn"
            onClick={handleDownload}
            title={i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_Download')}
          >
            <DownloadAndSaveOutlined />
          </button>

          <button
            className="action-btn"
            onClick={handleRegenerate}
            title={i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_Regenerate')}
          >
            <RefreshOutlined />
          </button>

          <button
            className="action-btn danger"
            onClick={handleDelete}
            title={i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_Delete')}
            disabled={isDeleting}
          >
            <DeleteOutlined />
          </button>
        </div>
      </div>
    );
  };

  return (
    <div 
      className={`image-item ${image.status} ${isDeleting ? 'deleting' : ''}`}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      <div className="image-container" onClick={handleImageClick}>
        <img 
          src={image.imageUrl} 
          alt={image.positivePrompt}
          className="image-preview"
        />
        
        {renderStatusOverlay()}
        
        {showActions && renderActionButtons()}
      </div>

      <div className="image-info">
        <div className="image-prompt">
          {image.positivePrompt}
        </div>
        
        <div className="image-meta">
          <div className="meta-row">
            <span className="meta-label">
              {i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_Size')}:
            </span>
            <span className="meta-value">{image.size}</span>
          </div>
          
          <div className="meta-row">
            <span className="meta-label">
              {i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_Style')}:
            </span>
            <span className="meta-value">{image.style}</span>
          </div>
          
          {image.createdAt && (
            <div className="meta-row">
              <span className="meta-label">
                {i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_Created')}:
              </span>
              <span className="meta-value">
                {formatCompletionTime(image.createdAt)}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ImageItem;
