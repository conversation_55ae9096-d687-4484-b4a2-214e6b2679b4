.image-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
  }

  &.deleting {
    opacity: 0.6;
    pointer-events: none;
  }

  .image-container {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
    cursor: pointer;

    .image-preview {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    &:hover .image-preview {
      transform: scale(1.05);
    }

    .status-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.7);
      color: white;

      &.processing {
        .progress-circle {
          position: relative;
          margin-bottom: 12px;

          .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 12px;
            font-weight: 600;
          }
        }

        .status-text {
          font-size: 14px;
          font-weight: 500;
        }
      }

      &.failed {
        .failed-icon {
          font-size: 32px;
          margin-bottom: 8px;
        }

        .status-text {
          font-size: 14px;
          color: #ff6b6b;
        }
      }
    }

    .action-buttons {
      position: absolute;
      top: 8px;
      right: 8px;
      background: rgba(0, 0, 0, 0.8);
      border-radius: 8px;
      padding: 6px;
      opacity: 0;
      transform: translateY(-10px);
      transition: all 0.3s ease;

      .action-row {
        display: flex;
        gap: 4px;
      }

      .action-btn {
        width: 32px;
        height: 32px;
        border: none;
        border-radius: 6px;
        background: rgba(255, 255, 255, 0.1);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 14px;

        &:hover:not(:disabled) {
          background: rgba(255, 255, 255, 0.2);
          transform: scale(1.1);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        &.rate-btn {
          &.active {
            background: #667eea;
            color: white;
          }

          &:hover:not(:disabled) {
            background: #667eea;
          }
        }

        &.danger {
          &:hover:not(:disabled) {
            background: #ff6b6b;
          }
        }
      }
    }

    &:hover .action-buttons {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .image-info {
    padding: 16px;

    .image-prompt {
      font-size: 14px;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.9);
      line-height: 1.4;
      margin-bottom: 12px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .image-meta {
      display: flex;
      flex-direction: column;
      gap: 6px;

      .meta-row {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;

        .meta-label {
          color: rgba(255, 255, 255, 0.5);
          min-width: 0;
          flex-shrink: 0;
        }

        .meta-value {
          color: rgba(255, 255, 255, 0.8);
          font-weight: 500;
          min-width: 0;
          flex: 1;
        }
      }
    }
  }

  // Status specific styles
  &.processing {
    .image-container {
      cursor: default;

      &:hover .image-preview {
        transform: none;
      }
    }
  }

  &.failed {
    border-color: rgba(255, 107, 107, 0.3);

    .image-container {
      cursor: default;

      &:hover .image-preview {
        transform: none;
      }
    }
  }

  &.finish {
    .image-container {
      cursor: pointer;
    }
  }

  // Mobile specific styles
  @media (max-width: 768px) {
    .image-container {
      height: 180px;

      .action-buttons {
        top: 6px;
        right: 6px;
        padding: 4px;

        .action-btn {
          width: 28px;
          height: 28px;
          font-size: 12px;
        }
      }

      .status-overlay {
        &.processing {
          .progress-circle {
            margin-bottom: 8px;

            svg {
              width: 32px;
              height: 32px;
            }

            .progress-text {
              font-size: 10px;
            }
          }

          .status-text {
            font-size: 12px;
          }
        }

        &.failed {
          .failed-icon {
            font-size: 28px;
            margin-bottom: 6px;
          }

          .status-text {
            font-size: 12px;
          }
        }
      }
    }

    .image-info {
      padding: 12px;

      .image-prompt {
        font-size: 13px;
        margin-bottom: 10px;
      }

      .image-meta {
        gap: 4px;

        .meta-row {
          font-size: 11px;
          gap: 6px;
        }
      }
    }
  }

  // PC specific styles
  @media (min-width: 769px) {
    .image-container {
      height: 220px;

      .action-buttons {
        .action-btn {
          width: 36px;
          height: 36px;
          font-size: 16px;
        }
      }
    }

    .image-info {
      padding: 18px;

      .image-prompt {
        font-size: 15px;
        margin-bottom: 14px;
      }

      .image-meta {
        gap: 8px;

        .meta-row {
          font-size: 13px;
          gap: 10px;
        }
      }
    }
  }
}
