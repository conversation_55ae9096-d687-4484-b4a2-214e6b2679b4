.ai-image-welcome-screen {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40px 20px;

  .welcome-content {
    text-align: center;
    max-width: 500px;
    width: 100%;

    .welcome-icon {
      margin-bottom: 32px;

      .icon-wrapper {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);

        .icon-text {
          font-size: 36px;
        }
      }
    }

    .welcome-title {
      font-size: 28px;
      font-weight: 700;
      color: rgba(255, 255, 255, 0.9);
      margin: 0 0 16px 0;
      line-height: 1.3;
    }

    .welcome-description {
      font-size: 16px;
      color: rgba(255, 255, 255, 0.7);
      margin: 0 0 32px 0;
      line-height: 1.5;
    }

    .feature-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-bottom: 40px;

      .feature-item {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
        padding: 12px 20px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.08);
          border-color: rgba(255, 255, 255, 0.2);
        }

        .feature-icon {
          font-size: 20px;
          flex-shrink: 0;
        }

        .feature-text {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.8);
          font-weight: 500;
        }
      }
    }

    .welcome-actions {
      .get-started-btn {
        width: 100%;
        max-width: 280px;
        height: 48px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 12px;
        font-size: 16px;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }

  // Mobile specific styles
  @media (max-width: 768px) {
    min-height: 50vh;
    padding: 30px 16px;

    .welcome-content {
      max-width: 400px;

      .welcome-icon {
        margin-bottom: 24px;

        .icon-wrapper {
          width: 64px;
          height: 64px;
          border-radius: 16px;

          .icon-text {
            font-size: 28px;
          }
        }
      }

      .welcome-title {
        font-size: 22px;
        margin-bottom: 12px;
      }

      .welcome-description {
        font-size: 14px;
        margin-bottom: 24px;
      }

      .feature-list {
        gap: 12px;
        margin-bottom: 32px;

        .feature-item {
          padding: 10px 16px;
          gap: 10px;

          .feature-icon {
            font-size: 18px;
          }

          .feature-text {
            font-size: 13px;
          }
        }
      }

      .welcome-actions {
        .get-started-btn {
          height: 44px;
          font-size: 15px;
        }
      }
    }
  }

  // PC specific styles
  @media (min-width: 769px) {
    min-height: 70vh;
    padding: 50px 30px;

    .welcome-content {
      max-width: 600px;

      .welcome-icon {
        margin-bottom: 40px;

        .icon-wrapper {
          width: 96px;
          height: 96px;
          border-radius: 24px;

          .icon-text {
            font-size: 44px;
          }
        }
      }

      .welcome-title {
        font-size: 32px;
        margin-bottom: 20px;
      }

      .welcome-description {
        font-size: 18px;
        margin-bottom: 40px;
      }

      .feature-list {
        gap: 20px;
        margin-bottom: 48px;

        .feature-item {
          padding: 16px 24px;
          gap: 16px;

          .feature-icon {
            font-size: 24px;
          }

          .feature-text {
            font-size: 16px;
          }
        }
      }

      .welcome-actions {
        .get-started-btn {
          height: 52px;
          font-size: 17px;
        }
      }
    }
  }
}
