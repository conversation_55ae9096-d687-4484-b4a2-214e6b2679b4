import { i18next } from '@ali/dingtalk-i18n';
import React from 'react';
import { Button } from 'dingtalk-design-mobile';
import './index.less';

interface WelcomeScreenProps {
  hasGenerateBtn: boolean;
  onGetStarted: () => void;
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  hasGenerateBtn,
  onGetStarted,
}) => {
  return (
    <div className="ai-image-welcome-screen">
      <div className="welcome-content">
        <div className="welcome-icon">
          <div className="icon-wrapper">
            <span className="icon-text">🎨</span>
          </div>
        </div>

        <h1 className="welcome-title">
          {i18next.t('j-dingtalk-web_pages_aiImage_components_WelcomeScreen_AIImageGeneration')}
        </h1>

        <p className="welcome-description">
          {i18next.t('j-dingtalk-web_pages_aiImage_components_WelcomeScreen_CreateBeautifulImages')}
        </p>

        <div className="feature-list">
          <div className="feature-item">
            <span className="feature-icon">✨</span>
            <span className="feature-text">
              {i18next.t('j-dingtalk-web_pages_aiImage_components_WelcomeScreen_TextToImage')}
            </span>
          </div>
          <div className="feature-item">
            <span className="feature-icon">🖼️</span>
            <span className="feature-text">
              {i18next.t('j-dingtalk-web_pages_aiImage_components_WelcomeScreen_MultipleFormats')}
            </span>
          </div>
          <div className="feature-item">
            <span className="feature-icon">🎯</span>
            <span className="feature-text">
              {i18next.t('j-dingtalk-web_pages_aiImage_components_WelcomeScreen_HighQuality')}
            </span>
          </div>
        </div>

        {hasGenerateBtn && (
          <div className="welcome-actions">
            <Button
              type="primary"
              size="large"
              className="get-started-btn"
              onClick={onGetStarted}
            >
              {i18next.t('j-dingtalk-web_pages_aiImage_components_WelcomeScreen_StartCreating')}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default WelcomeScreen;
