.ai-image-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  color: white;

  // Mobile layout
  .ai-image-mobile-layout {
    padding: 0;
  }

  // PC layout
  .ai-image-pc-layout {
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: 0;
    min-height: 100vh;

    .ai-image-left-panel {
      background: rgba(0, 0, 0, 0.2);
      border-right: 1px solid rgba(255, 255, 255, 0.1);
      overflow-y: auto;
      max-height: 100vh;
    }

    .ai-image-right-panel {
      overflow-y: auto;
      max-height: 100vh;
      padding: 20px;
    }
  }

  // Mobile specific styles
  @media (max-width: 768px) {
    .ai-image-pc-layout {
      display: block;

      .ai-image-left-panel,
      .ai-image-right-panel {
        max-height: none;
        overflow-y: visible;
      }

      .ai-image-left-panel {
        background: transparent;
        border-right: none;
      }

      .ai-image-right-panel {
        padding: 0;
      }
    }
  }

  // Tablet and small desktop
  @media (min-width: 769px) and (max-width: 1024px) {
    .ai-image-pc-layout {
      grid-template-columns: 350px 1fr;
    }
  }

  // Large desktop
  @media (min-width: 1200px) {
    .ai-image-pc-layout {
      grid-template-columns: 450px 1fr;
      gap: 0;

      .ai-image-right-panel {
        padding: 32px;
      }
    }
  }
}
