import { i18next } from '@ali/dingtalk-i18n';
import React, { useEffect, useState } from 'react';
import theme, { IThemeType } from 'dingtalk-theme';
import { isDingTalk, isPc, isMobileDevice, setPageTitle } from '@/utils/jsapi';
import { sendUT } from '@/utils/trace';
import { getMaterialStats, listAllMaterials } from '@/apis/material';
import $setRight from '@ali/dingtalk-jsapi/api/biz/navigation/setRight';
import Loading from '@/components/Loading';
import MaterialSelector from './components/MaterialSelector';
import RecentMaterials from './components/RecentMaterials';
import StatsOverview from './components/StatsOverview';
import { MaterialStats, AIMaterial } from '@/types/aiMaterial';
import './index.less';

theme.setTheme(IThemeType.dark);

interface AIStudioState {
  isLoading: boolean;
  stats: MaterialStats | null;
  recentMaterials: AIMaterial[];
  error: string | null;
}

const AIStudioPage: React.FC = () => {
  const [state, setState] = useState<AIStudioState>({
    isLoading: true,
    stats: null,
    recentMaterials: [],
    error: null,
  });

  // Initialize page
  useEffect(() => {
    // Set page title
    setPageTitle('AI素材设计');

    // Set navigation for mobile
    if (isDingTalk() && isMobileDevice()) {
      $setRight({
        show: false,
      });
    }

    // Load initial data
    loadInitialData();

    // Send page view event
    sendUT('ai_studio_page_view', {
      device: isMobileDevice() ? 'mobile' : 'pc',
    });
  }, []);

  const loadInitialData = async () => {
    try {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      // Load stats and recent materials in parallel
      const [statsResult, materialsResult] = await Promise.all([
        getMaterialStats(),
        listAllMaterials({ pageSize: 6 }), // Get recent 6 materials
      ]);

      if (statsResult.success && materialsResult.success) {
        setState((prev) => ({
          ...prev,
          isLoading: false,
          stats: statsResult.stats || null,
          recentMaterials: materialsResult.list || [],
        }));
      } else {
        throw new Error(statsResult.errorMsg || materialsResult.errorMsg || 'Failed to load data');
      }
    } catch (error) {
      console.error('Failed to load initial data:', error);
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : i18next.t('j-dingtalk-web_pages_aiStudio_FailedToLoadData'),
      }));
    }
  };

  const handleNavigateToVideo = () => {
    sendUT('ai_studio_navigate_to_video', {
      device: isMobileDevice() ? 'mobile' : 'pc',
    });

    // Navigate to aiVideo page
    window.location.href = '/aiVideo/index.html';
  };

  const handleNavigateToImage = () => {
    sendUT('ai_studio_navigate_to_image', {
      device: isMobileDevice() ? 'mobile' : 'pc',
    });

    // Navigate to ai-image page
    window.location.href = '/ai-image/index.html';
  };

  const handleRefresh = () => {
    loadInitialData();
  };

  // Render loading state
  if (state.isLoading) {
    return <Loading text={i18next.t('j-dingtalk-web_pages_aiStudio_Loading')} />;
  }

  // Render error state
  if (state.error) {
    return (
      <div className="ai-studio-error">
        <div className="error-content">
          <div className="error-message">{state.error}</div>
          <button className="retry-btn" onClick={handleRefresh}>
            {i18next.t('j-dingtalk-web_pages_aiStudio_Retry')}
          </button>
        </div>
      </div>
    );
  }

  // Render main content
  const renderContent = () => {
    if (isPc) {
      return (
        <div className="ai-studio-pc-layout">
          <div className="ai-studio-left-panel">
            <MaterialSelector
              onNavigateToVideo={handleNavigateToVideo}
              onNavigateToImage={handleNavigateToImage}
            />
            {state.stats && (
              <StatsOverview stats={state.stats} />
            )}
          </div>
          <div className="ai-studio-right-panel">
            <RecentMaterials
              materials={state.recentMaterials}
              onRefresh={handleRefresh}
            />
          </div>
        </div>
      );
    } else {
      return (
        <div className="ai-studio-mobile-layout">
          <MaterialSelector
            onNavigateToVideo={handleNavigateToVideo}
            onNavigateToImage={handleNavigateToImage}
          />
          <RecentMaterials
            materials={state.recentMaterials}
            onRefresh={handleRefresh}
          />
          {state.stats && (
            <StatsOverview stats={state.stats} />
          )}
        </div>
      );
    }
  };

  return (
    <div className="ai-studio-page">
      <div className="ai-studio-header">
        <h1 className="page-title">
          {i18next.t('j-dingtalk-web_pages_aiStudio_AIStudioTitle')}
        </h1>
      </div>
      {renderContent()}
    </div>
  );
};

export default AIStudioPage;
