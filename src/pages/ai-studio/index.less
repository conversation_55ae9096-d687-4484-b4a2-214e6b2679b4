.ai-studio-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  color: white;
  padding: 0;

  .ai-studio-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .page-title {
      font-size: 24px;
      font-weight: 700;
      margin: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }

  // Error state
  .ai-studio-error {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    padding: 20px;

    .error-content {
      text-align: center;
      max-width: 400px;

      .error-message {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 20px;
        line-height: 1.5;
      }

      .retry-btn {
        background: #FF0E53;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 12px 24px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: #e00c47;
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }

  // Mobile layout
  .ai-studio-mobile-layout {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  // PC layout
  .ai-studio-pc-layout {
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: 32px;
    padding: 32px;
    min-height: calc(100vh - 80px);

    .ai-studio-left-panel {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .ai-studio-right-panel {
      min-height: 0;
    }
  }

  // Mobile specific styles
  @media (max-width: 768px) {
    .ai-studio-header {
      padding: 16px;

      .page-title {
        font-size: 20px;
      }
    }

    .ai-studio-mobile-layout {
      padding: 16px;
      gap: 20px;
    }

    .ai-studio-error {
      min-height: 50vh;
      padding: 16px;

      .error-content {
        .error-message {
          font-size: 14px;
          margin-bottom: 16px;
        }

        .retry-btn {
          padding: 10px 20px;
          font-size: 13px;
        }
      }
    }
  }

  // Tablet and small desktop
  @media (min-width: 769px) and (max-width: 1024px) {
    .ai-studio-pc-layout {
      grid-template-columns: 350px 1fr;
      gap: 24px;
      padding: 24px;
    }
  }

  // Large desktop
  @media (min-width: 1200px) {
    .ai-studio-pc-layout {
      grid-template-columns: 450px 1fr;
      gap: 40px;
      padding: 40px;
    }
  }
}
