.stats-overview {
  .stats-title {
    font-size: 16px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 16px;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
  }

  .stats-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px 12px;
    text-align: center;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(255, 255, 255, 0.2);
    }

    .stats-value {
      font-size: 24px;
      font-weight: 700;
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: 4px;
      display: flex;
      align-items: baseline;
      justify-content: center;
      gap: 2px;

      .stats-suffix {
        font-size: 14px;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.6);
      }
    }

    .stats-label {
      font-size: 12px;
      color: rgba(255, 255, 255, 0.6);
      line-height: 1.3;
    }

    // Specific stat item styles
    &.total-stat {
      .stats-value {
        color: #4CAF50;
      }
    }

    &.monthly-stat {
      .stats-value {
        color: #2196F3;
      }
    }

    &.videos-stat {
      .stats-value {
        color: #FF6B35;
      }
    }

    &.images-stat {
      .stats-value {
        color: #9C27B0;
      }
    }

    &.success-stat {
      .stats-value {
        color: #FFC107;
      }
    }
  }

  // Mobile specific styles
  @media (max-width: 768px) {
    .stats-title {
      font-size: 14px;
      margin-bottom: 12px;
    }

    .stats-grid {
      grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
      gap: 8px;
    }

    .stats-item {
      padding: 12px 8px;

      .stats-value {
        font-size: 20px;
        margin-bottom: 2px;

        .stats-suffix {
          font-size: 12px;
        }
      }

      .stats-label {
        font-size: 11px;
      }
    }
  }

  // PC specific styles
  @media (min-width: 769px) {
    .stats-grid {
      grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
      gap: 16px;
    }

    .stats-item {
      padding: 20px 16px;

      .stats-value {
        font-size: 28px;
        margin-bottom: 6px;

        .stats-suffix {
          font-size: 16px;
        }
      }

      .stats-label {
        font-size: 13px;
      }
    }
  }
}
