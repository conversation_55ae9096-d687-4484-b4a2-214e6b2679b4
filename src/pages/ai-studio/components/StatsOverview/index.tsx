import { i18next } from '@ali/dingtalk-i18n';
import React from 'react';
import { MaterialStats } from '@/types/aiMaterial';
import './index.less';

interface StatsOverviewProps {
  stats: MaterialStats;
}

const StatsOverview: React.FC<StatsOverviewProps> = ({ stats }) => {
  const statsItems = [
    {
      key: 'total',
      label: i18next.t('j-dingtalk-web_pages_aiStudio_components_StatsOverview_TotalMaterials'),
      value: stats.totalCount,
      suffix: i18next.t('j-dingtalk-web_pages_aiStudio_components_StatsOverview_Items')
    },
    {
      key: 'monthly',
      label: i18next.t('j-dingtalk-web_pages_aiStudio_components_StatsOverview_ThisMonth'),
      value: stats.monthlyCount,
      suffix: i18next.t('j-dingtalk-web_pages_aiStudio_components_StatsOverview_Items')
    },
    {
      key: 'videos',
      label: i18next.t('j-dingtalk-web_pages_aiStudio_components_StatsOverview_Videos'),
      value: stats.videoCount,
      suffix: i18next.t('j-dingtalk-web_pages_aiStudio_components_StatsOverview_Items')
    },
    {
      key: 'images',
      label: i18next.t('j-dingtalk-web_pages_aiStudio_components_StatsOverview_Images'),
      value: stats.imageCount,
      suffix: i18next.t('j-dingtalk-web_pages_aiStudio_components_StatsOverview_Items')
    },
    {
      key: 'success',
      label: i18next.t('j-dingtalk-web_pages_aiStudio_components_StatsOverview_SuccessRate'),
      value: stats.successRate,
      suffix: '%'
    }
  ];

  return (
    <div className="stats-overview">
      <div className="stats-title">
        {i18next.t('j-dingtalk-web_pages_aiStudio_components_StatsOverview_CreationStats')}
      </div>
      
      <div className="stats-grid">
        {statsItems.map((item) => (
          <div key={item.key} className={`stats-item ${item.key}-stat`}>
            <div className="stats-value">
              {typeof item.value === 'number' && item.value % 1 !== 0 
                ? item.value.toFixed(1) 
                : item.value
              }
              <span className="stats-suffix">{item.suffix}</span>
            </div>
            <div className="stats-label">{item.label}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default StatsOverview;
