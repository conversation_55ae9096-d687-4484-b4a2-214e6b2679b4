import { i18next } from '@ali/dingtalk-i18n';
import React from 'react';
import { EditOutlined, PlayOutlined } from '@ali/ding-icons';
import './index.less';

interface MaterialSelectorProps {
  onNavigateToVideo: () => void;
  onNavigateToImage: () => void;
}

const MaterialSelector: React.FC<MaterialSelectorProps> = ({
  onNavigateToVideo,
  onNavigateToImage
}) => {
  return (
    <div className="material-selector">
      <div className="selector-title">
        {i18next.t('j-dingtalk-web_pages_aiStudio_components_MaterialSelector_ChooseYourCreation')}
      </div>
      
      <div className="feature-cards">
        {/* AI画像生成 - AI Image Generation */}
        <div 
          className="feature-card image-card"
          onClick={onNavigateToImage}
          role="button"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              onNavigateToImage();
            }
          }}
        >
          <div className="card-icon">
            <EditOutlined />
          </div>
          <div className="card-content">
            <h3 className="card-title">
              {i18next.t('j-dingtalk-web_pages_aiStudio_components_MaterialSelector_AIImageGeneration')}
            </h3>
            <p className="card-description">
              {i18next.t('j-dingtalk-web_pages_aiStudio_components_MaterialSelector_ImageGenerationDescription')}
            </p>
          </div>
          <div className="card-arrow">
            <span>›</span>
          </div>
        </div>

        {/* AIによる動画生成 - AI Video Generation */}
        <div 
          className="feature-card video-card"
          onClick={onNavigateToVideo}
          role="button"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              onNavigateToVideo();
            }
          }}
        >
          <div className="card-icon">
            <PlayOutlined />
          </div>
          <div className="card-content">
            <h3 className="card-title">
              {i18next.t('j-dingtalk-web_pages_aiStudio_components_MaterialSelector_AIVideoGeneration')}
            </h3>
            <p className="card-description">
              {i18next.t('j-dingtalk-web_pages_aiStudio_components_MaterialSelector_VideoGenerationDescription')}
            </p>
          </div>
          <div className="card-arrow">
            <span>›</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MaterialSelector;
