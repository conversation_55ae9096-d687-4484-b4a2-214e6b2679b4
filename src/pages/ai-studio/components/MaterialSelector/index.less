.material-selector {
  .selector-title {
    font-size: 18px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 24px;
    text-align: center;
  }

  .feature-cards {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .feature-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 16px;
    position: relative;
    overflow: hidden;

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
    }

    &:active {
      transform: translateY(0);
    }

    &:focus {
      outline: none;
      border-color: #FF0E53;
      box-shadow: 0 0 0 2px rgba(255, 14, 83, 0.2);
    }

    .card-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      flex-shrink: 0;
    }

    .card-content {
      flex: 1;
      min-width: 0;

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
        margin: 0 0 8px 0;
        line-height: 1.3;
      }

      .card-description {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.6);
        margin: 0;
        line-height: 1.4;
      }
    }

    .card-arrow {
      font-size: 20px;
      color: rgba(255, 255, 255, 0.4);
      flex-shrink: 0;
      transition: transform 0.3s ease;
    }

    &:hover .card-arrow {
      transform: translateX(4px);
      color: rgba(255, 255, 255, 0.6);
    }

    // Image card specific styles
    &.image-card {
      .card-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      &:hover {
        border-color: rgba(102, 126, 234, 0.3);
        box-shadow: 0 8px 24px rgba(102, 126, 234, 0.2);
      }
    }

    // Video card specific styles
    &.video-card {
      .card-icon {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
      }

      &:hover {
        border-color: rgba(245, 87, 108, 0.3);
        box-shadow: 0 8px 24px rgba(245, 87, 108, 0.2);
      }
    }
  }

  // Mobile specific styles
  @media (max-width: 768px) {
    .selector-title {
      font-size: 16px;
      margin-bottom: 20px;
    }

    .feature-cards {
      gap: 12px;
    }

    .feature-card {
      padding: 16px;
      gap: 12px;

      .card-icon {
        width: 40px;
        height: 40px;
        font-size: 20px;
      }

      .card-content {
        .card-title {
          font-size: 15px;
          margin-bottom: 6px;
        }

        .card-description {
          font-size: 13px;
        }
      }

      .card-arrow {
        font-size: 18px;
      }
    }
  }

  // PC specific styles
  @media (min-width: 769px) {
    .feature-cards {
      gap: 20px;
    }

    .feature-card {
      padding: 24px;
      gap: 20px;

      .card-icon {
        width: 56px;
        height: 56px;
        font-size: 28px;
      }

      .card-content {
        .card-title {
          font-size: 18px;
          margin-bottom: 10px;
        }

        .card-description {
          font-size: 15px;
        }
      }
    }
  }
}
