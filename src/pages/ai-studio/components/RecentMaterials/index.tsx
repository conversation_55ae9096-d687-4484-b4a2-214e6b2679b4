import { i18next } from '@ali/dingtalk-i18n';
import React from 'react';
import { Button } from 'dingtalk-design-mobile';
import { RefreshOutlined, PlayOutlined, EditOutlined } from '@ali/ding-icons';
import { AIMaterial } from '@/types/aiMaterial';
import { formatCompletionTime } from '@/utils/util';
import './index.less';

interface RecentMaterialsProps {
  materials: AIMaterial[];
  onRefresh: () => void;
}

const RecentMaterials: React.FC<RecentMaterialsProps> = ({
  materials,
  onRefresh
}) => {
  const handleMaterialClick = (material: AIMaterial) => {
    if (material.type === 'video') {
      window.location.href = '/aiVideo/index.html';
    } else {
      window.location.href = '/ai-image/index.html';
    }
  };

  const renderMaterialItem = (material: AIMaterial) => {
    const isVideo = material.type === 'video';
    const imageUrl = isVideo 
      ? (material as any).imageUrl 
      : (material as any).imageUrl;

    return (
      <div 
        key={material.uuid}
        className={`material-item ${material.type}-item`}
        onClick={() => handleMaterialClick(material)}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleMaterialClick(material);
          }
        }}
      >
        <div className="material-thumbnail">
          <img src={imageUrl} alt={material.positivePrompt} />
          <div className="material-type-badge">
            {isVideo ? <PlayOutlined /> : <EditOutlined />}
          </div>
          {material.status === 'processing' && (
            <div className="processing-overlay">
              <div className="processing-spinner" />
            </div>
          )}
        </div>
        
        <div className="material-info">
          <div className="material-prompt">
            {material.positivePrompt}
          </div>
          <div className="material-meta">
            <span className="material-type">
              {isVideo 
                ? i18next.t('j-dingtalk-web_pages_aiStudio_components_RecentMaterials_Video')
                : i18next.t('j-dingtalk-web_pages_aiStudio_components_RecentMaterials_Image')
              }
            </span>
            <span className="material-time">
              {formatCompletionTime(material.createdAt)}
            </span>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="recent-materials">
      <div className="section-header">
        <h2 className="section-title">
          {i18next.t('j-dingtalk-web_pages_aiStudio_components_RecentMaterials_RecentCreations')}
        </h2>
        <Button
          size="small"
          onClick={onRefresh}
          className="refresh-btn"
        >
          <RefreshOutlined />
        </Button>
      </div>

      {materials.length === 0 ? (
        <div className="empty-state">
          <div className="empty-icon">🎨</div>
          <div className="empty-title">
            {i18next.t('j-dingtalk-web_pages_aiStudio_components_RecentMaterials_NoMaterialsYet')}
          </div>
          <div className="empty-description">
            {i18next.t('j-dingtalk-web_pages_aiStudio_components_RecentMaterials_StartCreating')}
          </div>
        </div>
      ) : (
        <div className="materials-grid">
          {materials.map(renderMaterialItem)}
        </div>
      )}
    </div>
  );
};

export default RecentMaterials;
