.recent-materials {
  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.9);
      margin: 0;
    }

    .refresh-btn {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: rgba(255, 255, 255, 0.7);
      border-radius: 8px;
      min-width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
        color: rgba(255, 255, 255, 0.9);
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: rgba(255, 255, 255, 0.6);

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    .empty-title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 8px;
      color: rgba(255, 255, 255, 0.8);
    }

    .empty-description {
      font-size: 14px;
      line-height: 1.4;
    }
  }

  .materials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
  }

  .material-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
    }

    &:focus {
      outline: none;
      border-color: #FF0E53;
      box-shadow: 0 0 0 2px rgba(255, 14, 83, 0.2);
    }

    .material-thumbnail {
      position: relative;
      width: 100%;
      height: 160px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      .material-type-badge {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 28px;
        height: 28px;
        border-radius: 6px;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 14px;
      }

      .processing-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.6);
        display: flex;
        align-items: center;
        justify-content: center;

        .processing-spinner {
          width: 24px;
          height: 24px;
          border: 2px solid rgba(255, 255, 255, 0.3);
          border-top: 2px solid white;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
      }
    }

    .material-info {
      padding: 12px;

      .material-prompt {
        font-size: 14px;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.9);
        line-height: 1.4;
        margin-bottom: 8px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .material-meta {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.5);

        .material-type {
          background: rgba(255, 255, 255, 0.1);
          padding: 2px 8px;
          border-radius: 4px;
          font-size: 11px;
        }

        .material-time {
          font-size: 11px;
        }
      }
    }

    &:hover .material-thumbnail img {
      transform: scale(1.05);
    }

    // Video specific styles
    &.video-item {
      .material-type-badge {
        background: rgba(245, 87, 108, 0.8);
      }
    }

    // Image specific styles
    &.image-item {
      .material-type-badge {
        background: rgba(102, 126, 234, 0.8);
      }
    }
  }

  // Mobile specific styles
  @media (max-width: 768px) {
    .section-header {
      margin-bottom: 16px;

      .section-title {
        font-size: 16px;
      }

      .refresh-btn {
        min-width: 32px;
        height: 32px;
      }
    }

    .materials-grid {
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
      gap: 12px;
    }

    .material-item {
      .material-thumbnail {
        height: 140px;

        .material-type-badge {
          width: 24px;
          height: 24px;
          font-size: 12px;
        }
      }

      .material-info {
        padding: 10px;

        .material-prompt {
          font-size: 13px;
          margin-bottom: 6px;
        }
      }
    }

    .empty-state {
      padding: 30px 15px;

      .empty-icon {
        font-size: 40px;
        margin-bottom: 12px;
      }

      .empty-title {
        font-size: 15px;
      }

      .empty-description {
        font-size: 13px;
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
